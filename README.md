# Go RAG 基础工程（Qdrant + Eino）

本仓库根目录为主模块，提供基于 Qdrant 向量数据库与 CloudWeGo Eino 编排框架的 RAG（检索增强生成）系统基础骨架，遵循 Go 社区惯例（Standard Go Project Layout）。

- 向量数据库：Qdrant（github.com/qdrant/go-client/qdrant）
- LLM 编排：Eino（github.com/cloudwego/eino）

目录结构（简化）：
- cmd/           主程序入口（cmd/main.go）
- internal/
  - config/      配置管理（环境变量）
  - vector/qdrant/  Qdrant 客户端封装
  - orchestrator/eino/  Eino 编排占位
  - rag/         RAG 业务接口

快速开始：
1) 依赖准备
   - 推荐设置 GOPROXY（如在中国大陆网络环境）：
     - PowerShell（临时）：$env:GOPROXY="https://goproxy.cn,direct"
     - 永久：go env -w GOPROXY=https://goproxy.cn,direct
2) 安装依赖
   - go mod tidy
3) 运行
   - go run ./cmd

环境变量：
- QDRANT_HOST：默认 localhost
- QDRANT_PORT：默认 6334（gRPC）
- QDRANT_API_KEY：可选
- QDRANT_USE_TLS：默认 false

后续扩展建议：
- 在 internal/orchestrator/eino 中加入 ChatModel（OpenAI/自建模型）、ChatTemplate，并以 Graph/Workflow 组织流程
- 在 internal/vector/qdrant 中完善 collection 管理、索引参数、过滤检索
- 添加 Embedding 生成与入库（Upsert/UpdateBatch），将文本入库与检索串接
- 在 internal/rag 实现 Pipeline，将“检索→提示词→LLM→答案”打通

