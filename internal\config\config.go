package config

import (
	"fmt"
	"os"
	"path/filepath"
	"regexp"
	"strconv"

	"gopkg.in/yaml.v3"
)

type QdrantConfig struct {
	Host   string `yaml:"host"`
	Port   int    `yaml:"port"`
	APIKey string `yaml:"api_key"`
	UseTLS bool   `yaml:"use_tls"`
}

type Config struct {
	Qdrant QdrantConfig `yaml:"qdrant"`
}

func Load() Config {
	// 尝试加载YAML配置文件
	configPath := getConfigPath()
	if data, err := os.ReadFile(configPath); err == nil {
		return loadFromYAML(data)
	}

	// 如果YAML文件不存在，回退到环境变量
	return Config{
		Qdrant: QdrantConfig{
			Host:   getenv("QDRANT_HOST", "localhost"),
			Port:   getenvInt("QDRANT_PORT", 6334),
			APIKey: getenv("QDRANT_API_KEY", ""),
			UseTLS: getenvBool("QDRANT_USE_TLS", false),
		},
	}
}

// getConfigPath 获取配置文件路径
func getConfigPath() string {
	// 优先使用环境变量指定的配置文件路径
	if path := os.Getenv("CONFIG_PATH"); path != "" {
		return path
	}

	// 默认配置文件路径
	return filepath.Join("config", "config.yaml")
}

// loadFromYAML 从YAML数据加载配置
func loadFromYAML(data []byte) Config {
	var config Config

	// 替换环境变量占位符
	content := expandEnvVars(string(data))

	if err := yaml.Unmarshal([]byte(content), &config); err != nil {
		fmt.Printf("Warning: Failed to parse YAML config: %v, falling back to environment variables\n", err)
		return Config{
			Qdrant: QdrantConfig{
				Host:   getenv("QDRANT_HOST", "localhost"),
				Port:   getenvInt("QDRANT_PORT", 6334),
				APIKey: getenv("QDRANT_API_KEY", ""),
				UseTLS: getenvBool("QDRANT_USE_TLS", false),
			},
		}
	}

	return config
}

// expandEnvVars 展开环境变量占位符 ${VAR:default}
func expandEnvVars(content string) string {
	re := regexp.MustCompile(`\$\{([^}:]+):([^}]*)\}`)
	return re.ReplaceAllStringFunc(content, func(match string) string {
		parts := re.FindStringSubmatch(match)
		if len(parts) != 3 {
			return match
		}

		envVar := parts[1]
		defaultVal := parts[2]

		if value := os.Getenv(envVar); value != "" {
			return value
		}
		return defaultVal
	})
}

func getenv(key, def string) string {
	if v := os.Getenv(key); v != "" {
		return v
	}
	return def
}

func getenvInt(key string, def int) int {
	if v := os.Getenv(key); v != "" {
		if n, err := strconv.Atoi(v); err == nil {
			return n
		}
	}
	return def
}

func getenvBool(key string, def bool) bool {
	if v := os.Getenv(key); v != "" {
		if v == "1" || v == "true" || v == "TRUE" || v == "True" {
			return true
		}
		if v == "0" || v == "false" || v == "FALSE" || v == "False" {
			return false
		}
	}
	return def
}
