package qdrant

import (
	"context"
	"fmt"

	q "github.com/qdrant/go-client/qdrant"
)

// EnsureCollection 如果集合不存在则创建
func (c *Client) EnsureCollection(ctx context.Context, collection string, size uint32, distance q.Distance) error {
	// 检查是否已存在
	exists, err := c.grpc.CollectionExists(ctx, collection)
	if err != nil {
		return fmt.Errorf("check collection exists: %w", err)
	}
	if exists.GetExists() {
		return nil
	}
	err = c.grpc.CreateCollection(ctx, &q.CreateCollection{
		CollectionName: collection,
		VectorsConfig:  q.NewVectorsConfig(&q.VectorParams{Size: uint64(size), Distance: distance}),
	})
	if err != nil {
		return fmt.Errorf("create collection: %w", err)
	}
	return nil
}

// Point 表示要写入的点
type Point struct {
	ID      uint64
	Vector  []float32
	Payload map[string]any
}

// UpsertPoints 写入/更新向量与可选 payload
func (c *Client) UpsertPoints(ctx context.Context, collection string, pts []Point) error {
	var qpts []*q.PointStruct
	for _, p := range pts {
		qpts = append(qpts, &q.PointStruct{
			Id:      q.NewIDNum(p.ID),
			Vectors: q.NewVectors(p.Vector...),
			Payload: q.NewValueMap(p.Payload),
		})
	}
	_, err := c.grpc.Upsert(ctx, &q.UpsertPoints{CollectionName: collection, Points: qpts})
	if err != nil {
		return fmt.Errorf("upsert: %w", err)
	}
	return nil
}

// SearchTopK 在指定集合中做相似性检索
func (c *Client) SearchTopK(ctx context.Context, collection string, query []float32, topK uint64, withPayload bool) (*q.QueryResponse, error) {
	req := &q.QueryPoints{
		CollectionName: collection,
		Query:          q.NewQuery(query...),
		Limit:          topK,
	}
	if withPayload {
		req.WithPayload = q.NewWithPayload(true)
	}
	resp, err := c.grpc.Query(ctx, req)
	if err != nil {
		return nil, fmt.Errorf("query: %w", err)
	}
	return resp, nil
}
