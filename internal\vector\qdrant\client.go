package qdrant

import (
	"context"
	"fmt"

	q "github.com/qdrant/go-client/qdrant"

	"wkx/wuye/knowledgebase/internal/config"
)

type Client struct {
	grpc *q.Client
}

func NewClient(ctx context.Context, cfg config.QdrantConfig) (*Client, error) {
	c, err := q.NewClient(&q.Config{
		Host:   cfg.Host,
		Port:   cfg.Port,
		APIKey: cfg.APIKey,
		UseTLS: cfg.UseTLS,
	})
	if err != nil {
		return nil, fmt.Errorf("qdrant new client: %w", err)
	}
	return &Client{grpc: c}, nil
}

func (c *Client) Close() error {
	return c.grpc.Close()
}

// HealthCheck 执行一个简单的健康检查（ListCollections 或者 Health）
func (c *Client) HealthCheck(ctx context.Context) (bool, error) {
	_, err := c.grpc.ListCollections(ctx)
	if err != nil {
		return false, err
	}
	return true, nil
}

